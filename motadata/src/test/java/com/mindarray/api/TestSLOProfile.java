/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.InfoMessageConstants;
import com.mindarray.TestAPIUtil;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.SLOProfileConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import com.mindarray.visualization.VisualizationConstants;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;
import static com.mindarray.api.SLOCycle.SLO_PROFILE_ID;
import static com.mindarray.api.SLOProfile.*;
import static com.mindarray.slo.SLOConstants.SLOFrequency;
import static com.mindarray.slo.SLOConstants.SLOType;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;
import static org.apache.http.HttpStatus.SC_OK;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;

/**
 * Test class for SLO Profile API operations.
 * Tests all CRUD operations and validates different SLO profile types:
 * - Monitor Availability SLO
 * - Monitor Performance SLO  
 * - Instance Availability SLO
 * - Instance Performance SLO
 *
 * <AUTHOR> Thakkar
 */
@ExtendWith(VertxExtension.class)
@Execution(ExecutionMode.SAME_THREAD)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(60 * 1000)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestSLOProfile
{
    private static final String SLO_PROFILE_API_ENDPOINT = "/api/v1/settings/slo-profiles";
    private static final String SLO_CYCLES_API_ENDPOINT = "/api/v1/settings/slo-cycles";

    private static final JsonObject context = new JsonObject();
    
    private static final Logger LOGGER = new Logger(TestSLOProfile.class, MOTADATA_API, "Test SLO Profile");
    
    @BeforeEach
    void beforeEach(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));
        
        testContext.completeNow();
    }
    
    /**
     * Test creating Monitor Availability SLO Profile (Payload 1)
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testCreateMonitorAvailabilitySLOProfile(VertxTestContext testContext, TestInfo testInfo)
    {
        var timestamp = System.currentTimeMillis() / 1000;
        
        var payload = new JsonObject()
            .put(SLO_PROFILE_NAME, "MonitorAvailabilitySLO")
            .put(SLO_PROFILE_TYPE, SLOType.AVAILABILITY.getName())
            .put(SLO_PROFILE_BUSINESS_SERVICE_NAME, "Airtel")
            .put(SLO_PROFILE_TAGS, new JsonArray().add("monitor").add("availability"))
            .put(SLO_PROFILE_CONTEXT, new JsonObject()
                .put(ENTITY_TYPE, APIConstants.Entity.GROUP.getName())
                .put(METRIC, "status")
                .put(SLO_INSTANCE, NMSConstants.InstanceType.MONITOR.name())
                .put(ENTITIES, new JsonArray().add(10000000000017L))
                .put(SLO_TARGET, 90)
                .put(SLO_WARNING, 95)
                .put(FILTERS, new JsonObject().put(DATA_FILTER, new JsonObject()))
                .put(SLO_FREQUENCY, SLOFrequency.DAILY.getName()))
            .put(SLO_PROFILE_START_TIME, timestamp);
            
        TestAPIUtil.post(SLO_PROFILE_API_ENDPOINT, payload, testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertCreateEntityTestResult(SLOProfileConfigStore.getStore(), response.bodyAsJsonObject(), 
                String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.SLO_PROFILE.getName()), 
                LOGGER, testInfo.getTestMethod().get().getName());
                
            context.put("monitorAvailabilityId", response.bodyAsJsonObject().getLong(ID));
            
            testContext.completeNow();
        })));
    }
    
    /**
     * Test creating Monitor Performance SLO Profile (Payload 2)
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testCreateMonitorPerformanceSLOProfile(VertxTestContext testContext, TestInfo testInfo)
    {
        var timestamp = System.currentTimeMillis() / 1000;
        
        var payload = new JsonObject()
            .put(SLO_PROFILE_NAME, "MonitorPerformanceSLO")
            .put(SLO_PROFILE_TYPE, SLOType.PERFORMANCE.getName())
            .put(SLO_PROFILE_BUSINESS_SERVICE_NAME, "Airtel-Performance")
            .put(SLO_PROFILE_CONTEXT, new JsonObject()
                .put(ENTITY_TYPE, APIConstants.Entity.GROUP.getName())
                .put(METRIC, "system.cpu.percent")
                .put(SLO_INSTANCE, NMSConstants.InstanceType.MONITOR.name())
                .put(SLO_SEVERITY, new JsonObject()
                    .put(Severity.CRITICAL.name(), new JsonObject()
                        .put("policy.condition", ">=")
                        .put("policy.threshold", "15")))
                .put(ENTITIES, new JsonArray().add(10000000000017L))
                .put(SLO_TARGET, 90)
                .put(SLO_WARNING, 95)
                .put(FILTERS, new JsonObject().put(DATA_FILTER, new JsonObject()))
                .put(SLO_FREQUENCY, SLOFrequency.MONTHLY.getName()))
            .put(SLO_PROFILE_START_TIME, timestamp);
            
        TestAPIUtil.post(SLO_PROFILE_API_ENDPOINT, payload, testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertCreateEntityTestResult(SLOProfileConfigStore.getStore(), response.bodyAsJsonObject(), 
                String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.SLO_PROFILE.getName()), 
                LOGGER, testInfo.getTestMethod().get().getName());
                
            context.put("monitorPerformanceId", response.bodyAsJsonObject().getLong(ID));
            
            testContext.completeNow();
        })));
    }
    
    /**
     * Test creating Instance Availability SLO Profile (Payload 3)
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testCreateInstanceAvailabilitySLOProfile(VertxTestContext testContext, TestInfo testInfo)
    {
        var timestamp = System.currentTimeMillis() / 1000;
        
        var payload = new JsonObject()
            .put(SLO_PROFILE_NAME, "InstanceAvailabilitySLO")
            .put(SLO_PROFILE_TYPE, SLOType.AVAILABILITY.getName())
            .put(SLO_PROFILE_BUSINESS_SERVICE_NAME, "Airtel")
            .put(SLO_PROFILE_CONTEXT, new JsonObject()
                .put(ENTITY_TYPE, "monitor")
                .put(METRIC, "status")
                .put(SLO_INSTANCE, NMSConstants.InstanceType.PROCESS.name())
                .put(VisualizationConstants.PLUGINS, new JsonArray()
                    .add("linuxprocess")
                    .add("windowsprocess")
                    .add("solarisprocess")
                    .add("ibmaixprocess")
                    .add("hpuxprocess"))
                .put(ENTITIES, new JsonArray().add(77789039783L))
                .put(SLO_TARGET, 90)
                .put(SLO_WARNING, 95)
                .put(FILTERS, new JsonObject().put(DATA_FILTER, new JsonObject()))
                .put(SLO_FREQUENCY, SLOFrequency.DAILY.getName()))
            .put(SLO_PROFILE_START_TIME, timestamp);
            
        TestAPIUtil.post(SLO_PROFILE_API_ENDPOINT, payload, testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertCreateEntityTestResult(SLOProfileConfigStore.getStore(), response.bodyAsJsonObject(), 
                String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.SLO_PROFILE.getName()), 
                LOGGER, testInfo.getTestMethod().get().getName());
                
            context.put("instanceAvailabilityId", response.bodyAsJsonObject().getLong(ID));
            
            testContext.completeNow();
        })));
    }
    
    /**
     * Test creating Instance Performance SLO Profile (Payload 4)
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testCreateInstancePerformanceSLOProfile(VertxTestContext testContext, TestInfo testInfo)
    {
        var timestamp = System.currentTimeMillis() / 1000;

        var payload = new JsonObject()
            .put(SLO_PROFILE_NAME, "InstancePerformanceSLO")
            .put(SLO_PROFILE_TYPE, SLOType.PERFORMANCE.getName())
            .put(SLO_PROFILE_BUSINESS_SERVICE_NAME, "Airtel")
            .put(SLO_PROFILE_CONTEXT, new JsonObject()
                .put(ENTITY_TYPE, "monitor")
                .put(METRIC, "system.process~cpu.percent")
                .put(SLO_INSTANCE, NMSConstants.InstanceType.PROCESS.name())
                .put(SLO_SEVERITY, new JsonObject()
                    .put(Severity.CRITICAL.name(), new JsonObject()
                        .put("policy.condition", ">=")
                        .put("policy.threshold", "0")))
                .put(VisualizationConstants.PLUGINS, new JsonArray()
                    .add("linuxprocess")
                    .add("windowsprocess")
                    .add("solarisprocess")
                    .add("ibmaixprocess")
                    .add("hpuxprocess"))
                .put(ENTITIES, new JsonArray().add(77789039783L))
                .put(SLO_TARGET, 90)
                .put(SLO_WARNING, 95)
                .put(FILTERS, new JsonObject().put(DATA_FILTER, new JsonObject()))
                .put(SLO_FREQUENCY, SLOFrequency.DAILY.getName()))
            .put(SLO_PROFILE_START_TIME, timestamp);

        TestAPIUtil.post(SLO_PROFILE_API_ENDPOINT, payload, testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertCreateEntityTestResult(SLOProfileConfigStore.getStore(), response.bodyAsJsonObject(),
                String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.SLO_PROFILE.getName()),
                LOGGER, testInfo.getTestMethod().get().getName());

            context.put("instancePerformanceId", response.bodyAsJsonObject().getLong(ID));

            testContext.completeNow();
        })));
    }

    /**
     * Test getting all SLO profiles
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testGetAllSLOProfiles(VertxTestContext testContext)
    {
        TestAPIUtil.get(SLO_PROFILE_API_ENDPOINT, testContext.succeeding(response -> testContext.verify(() ->
        {
            assertEquals(SC_OK, response.statusCode());

            var body = response.bodyAsJsonObject();

            Assertions.assertNotNull(body);

            assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

            var entities = body.getJsonArray(RESULT);

            Assertions.assertNotNull(entities);

            assertFalse(entities.isEmpty());

            var items = SLOProfileConfigStore.getStore().getItems();

            Assertions.assertNotNull(items);

            assertFalse(items.isEmpty());

            for (var index = 0; index < items.size(); index++)
            {
                var item = items.getJsonObject(index);

                item.remove(SLO_PROFILE_TAGS);
            }

            TestAPIUtil.assertTestResult(entities, items, new JsonArray().add(SLO_PROFILE_TAGS));

            testContext.completeNow();
        })));
    }

    /**
     * Test getting a single SLO profile by ID
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testGetSLOProfile(VertxTestContext testContext, TestInfo testInfo)
    {
        var profileId = context.getLong("monitorAvailabilityId");

        TestAPIUtil.get(SLO_PROFILE_API_ENDPOINT + "/" + profileId, testContext.succeeding(response -> testContext.verify(() ->
        {
            assertEquals(SC_OK, response.statusCode());

            var body = response.bodyAsJsonObject();

            Assertions.assertNotNull(body);

            assertEquals(SC_OK, body.getInteger(RESPONSE_CODE));

            body = body.getJsonObject(RESULT);

            Assertions.assertNotNull(body);

            assertFalse(body.isEmpty());

            assertEquals(profileId, body.getLong(GlobalConstants.ID));

            var item = SLOProfileConfigStore.getStore().getItem(profileId);

            Assertions.assertNotNull(item);

            assertFalse(item.isEmpty());

            APIUtil.removeDefaultParameters(body);

            APIUtil.removeDefaultParameters(item);

            body.remove(SLO_PROFILE_TAGS);

            item.remove(SLO_PROFILE_TAGS);

            Assertions.assertEquals(CommonUtil.removeSensitiveFields(body, true), CommonUtil.removeSensitiveFields(item, true));

            testContext.completeNow();
        })));
    }

    /**
     * Test getting active SLO profiles with filter
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testGetActiveSLOProfiles(VertxTestContext testContext)
    {
        var filterParam = "{\"active\":\"yes\"}";

        TestAPIUtil.get(SLO_PROFILE_API_ENDPOINT + "?filter=" + filterParam, testContext.succeeding(response -> testContext.verify(() ->
        {
            var result = response.bodyAsJsonObject();

            Assertions.assertEquals(200, result.getInteger(RESPONSE_CODE));
            Assertions.assertEquals(STATUS_SUCCEED, result.getString(STATUS));
            Assertions.assertTrue(result.containsKey(RESULT));

            var profiles = result.getJsonArray(RESULT);
            Assertions.assertNotNull(profiles);

            testContext.completeNow();
        })));
    }

    /**
     * Test getting all SLO cycles for a specific SLO profile
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testGetSLOCyclesForProfile(VertxTestContext testContext, TestInfo testInfo)
    {
        var profileId = context.getLong("monitorAvailabilityId");

        var filterParam = String.format("{\"key\":\"%s\", \"value\":[%d]}", SLO_PROFILE_ID, profileId);

        TestAPIUtil.get(SLO_CYCLES_API_ENDPOINT + "?filter=" + filterParam, testContext.succeeding(response -> testContext.verify(() ->
        {
            var result = response.bodyAsJsonObject();

            LOGGER.info(String.format("%s: response: %s", testInfo.getTestMethod().get().getName(), result.encode()));

            Assertions.assertEquals(200, result.getInteger(RESPONSE_CODE));
            Assertions.assertEquals(STATUS_SUCCEED, result.getString(STATUS));
            Assertions.assertTrue(result.containsKey(RESULT));

            var cycles = result.getJsonArray(RESULT);
            Assertions.assertNotNull(cycles);

            // Verify that all returned cycles belong to the specified SLO profile
            for (int i = 0; i < cycles.size(); i++)
            {
                var cycle = cycles.getJsonObject(i);

                Assertions.assertEquals(profileId, cycle.getLong(SLO_PROFILE_ID));
            }

            testContext.completeNow();
        })));
    }

    /**
     * Test creating SLO profile with invalid type - should fail validation
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testCreateSLOProfileWithInvalidType(VertxTestContext testContext)
    {
        var timestamp = System.currentTimeMillis() / 1000;

        var invalidPayload = new JsonObject()
            .put(SLO_PROFILE_NAME, "InvalidTypeSLO")
            .put(SLO_PROFILE_TYPE, "InvalidType") // Invalid type - should be Availability or Performance
            .put(SLO_PROFILE_BUSINESS_SERVICE_NAME, "TestService")
            .put(SLO_PROFILE_CONTEXT, new JsonObject()
                .put(ENTITY_TYPE, APIConstants.Entity.GROUP.getName())
                .put(METRIC, "status")
                .put(SLO_INSTANCE, NMSConstants.InstanceType.MONITOR.name())
                .put(ENTITIES, new JsonArray().add(10000000000017L))
                .put(SLO_TARGET, 90)
                .put(SLO_WARNING, 95)
                .put(FILTERS, new JsonObject().put(DATA_FILTER, new JsonObject()))
                .put(SLO_FREQUENCY, SLOFrequency.DAILY.getName()))
            .put(SLO_PROFILE_START_TIME, timestamp);

        TestAPIUtil.post(SLO_PROFILE_API_ENDPOINT, invalidPayload, testContext.succeeding(response -> testContext.verify(() ->
        {
            var result = response.bodyAsJsonObject();

            Assertions.assertEquals(SC_BAD_REQUEST, result.getInteger(RESPONSE_CODE));
            Assertions.assertEquals(STATUS_FAIL, result.getString(STATUS));
            Assertions.assertTrue(result.containsKey(MESSAGE));

            testContext.completeNow();
        })));
    }

    /**
     * Test creating SLO profile with missing required fields - should fail validation
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testCreateSLOProfileWithMissingRequiredFields(VertxTestContext testContext)
    {
        var incompletePayload = new JsonObject()
            .put(SLO_PROFILE_NAME, "IncompleteSLO")
            // Missing required fields: slo.profile.type, slo.profile.business.service.name, slo.profile.context, slo.profile.start.time
            .put(SLO_PROFILE_TAGS, new JsonArray().add("test"));

        TestAPIUtil.post(SLO_PROFILE_API_ENDPOINT, incompletePayload, testContext.succeeding(response -> testContext.verify(() ->
        {
            var result = response.bodyAsJsonObject();

            Assertions.assertEquals(SC_BAD_REQUEST, result.getInteger(RESPONSE_CODE));
            Assertions.assertEquals(STATUS_FAIL, result.getString(STATUS));
            Assertions.assertTrue(result.containsKey(MESSAGE));

            testContext.completeNow();
        })));
    }

    /**
     * Test creating SLO profile with duplicate name - should fail uniqueness validation
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    void testCreateSLOProfileWithDuplicateName(VertxTestContext testContext)
    {
        var timestamp = System.currentTimeMillis() / 1000;

        var duplicatePayload = new JsonObject()
            .put(SLO_PROFILE_NAME, "MonitorAvailabilitySLO") // Duplicate name from first test
            .put(SLO_PROFILE_TYPE, SLOType.AVAILABILITY.getName())
            .put(SLO_PROFILE_BUSINESS_SERVICE_NAME, "TestService")
            .put(SLO_PROFILE_CONTEXT, new JsonObject()
                .put(ENTITY_TYPE, APIConstants.Entity.GROUP.getName())
                .put(METRIC, "status")
                .put(SLO_INSTANCE, NMSConstants.InstanceType.MONITOR.name())
                .put(ENTITIES, new JsonArray().add(10000000000017L))
                .put(SLO_TARGET, 90)
                .put(SLO_WARNING, 95)
                .put(FILTERS, new JsonObject().put(DATA_FILTER, new JsonObject()))
                .put(SLO_FREQUENCY, SLOFrequency.DAILY.getName()))
            .put(SLO_PROFILE_START_TIME, timestamp);

        TestAPIUtil.post(SLO_PROFILE_API_ENDPOINT, duplicatePayload, testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertAlreadyExistedEntityTestResult(response.bodyAsJsonObject(),
                String.format(ErrorMessageConstants.API_FIELD_UNIQUE_RULE, "SLO Profile Name"),
                SLOProfileConfigStore.getStore(), SLO_PROFILE_NAME, "MonitorAvailabilitySLO");

            testContext.completeNow();
        })));
    }

    /**
     * Test deleting SLO profiles
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    void testDeleteSLOProfiles(VertxTestContext testContext)
    {
        var profileId = context.getLong("instancePerformanceId");

        TestAPIUtil.delete(SLO_PROFILE_API_ENDPOINT + "/" + profileId, testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertDeleteEntityTestResult(SLOProfileConfigStore.getStore(), response.bodyAsJsonObject(),
                String.format(InfoMessageConstants.ENTITY_DELETED, APIConstants.Entity.SLO_PROFILE.getName()));

            testContext.completeNow();
        })));
    }

    /**
     * Test tag handling in SLO profile creation and retrieval
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(13)
    void testSLOProfileTagHandling(VertxTestContext testContext, TestInfo testInfo)
    {
        var timestamp = System.currentTimeMillis() / 1000;

        var payloadWithTags = new JsonObject()
            .put(SLO_PROFILE_NAME, "TaggedSLOProfile")
            .put(SLO_PROFILE_TYPE, SLOType.AVAILABILITY.getName())
            .put(SLO_PROFILE_BUSINESS_SERVICE_NAME, "TaggedService")
            .put(SLO_PROFILE_TAGS, new JsonArray().add("tag1").add("tag2").add("tag3"))
            .put(SLO_PROFILE_CONTEXT, new JsonObject()
                .put(ENTITY_TYPE, APIConstants.Entity.GROUP.getName())
                .put(METRIC, "status")
                .put(SLO_INSTANCE, NMSConstants.InstanceType.MONITOR.name())
                .put(ENTITIES, new JsonArray().add(10000000000017L))
                .put(SLO_TARGET, 90)
                .put(SLO_WARNING, 95)
                .put(FILTERS, new JsonObject().put(DATA_FILTER, new JsonObject()))
                .put(SLO_FREQUENCY, SLOFrequency.DAILY.getName()))
            .put(SLO_PROFILE_START_TIME, timestamp);

        TestAPIUtil.post(SLO_PROFILE_API_ENDPOINT, payloadWithTags, testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertCreateEntityTestResult(SLOProfileConfigStore.getStore(), response.bodyAsJsonObject(),
                String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.SLO_PROFILE.getName()),
                LOGGER, testInfo.getTestMethod().get().getName());

            var createdId = response.bodyAsJsonObject().getLong(ID);

            // Verify tags are properly handled by retrieving the created profile
            TestAPIUtil.get(SLO_PROFILE_API_ENDPOINT + "/" + createdId, testContext.succeeding(getResponse -> testContext.verify(() ->
            {
                var retrievedProfile = getResponse.bodyAsJsonObject().getJsonObject(RESULT);

                Assertions.assertTrue(retrievedProfile.containsKey(SLO_PROFILE_TAGS));

                var tags = retrievedProfile.getJsonArray(SLO_PROFILE_TAGS);
                Assertions.assertNotNull(tags);
                Assertions.assertTrue(tags.size() >= 3); // Should contain at least the 3 tags we added

                testContext.completeNow();
            })));
        })));
    }
}
